import React, { useState, useRef, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Search, X, Clock, FileText, Users, BookText, Folder, Loader2, Command as CommandIcon } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { cn } from "@/lib/utils";
import { useClerkWorkspace } from "@/lib/clerk-workspace-provider";
import { ContractService, TemplateService } from "@/services/api-services";
import { useDebounce } from "@/hooks/useDebounce";

export interface SearchResult {
  id: string;
  title: string;
  type: "contract" | "template" | "clause" | "user";
  description?: string;
  status?: string;
  url: string;
  metadata?: Record<string, any>;
}

export interface SearchCategory {
  key: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
}

const searchCategories: SearchCategory[] = [
  { key: "all", label: "All", icon: Search, color: "text-muted-foreground" },
  { key: "contracts", label: "Contracts", icon: FileText, color: "text-blue-600" },
  { key: "templates", label: "Templates", icon: BookText, color: "text-green-600" },
  { key: "clauses", label: "Clauses", icon: Folder, color: "text-purple-600" },
  { key: "users", label: "Users", icon: Users, color: "text-orange-600" },
];

interface GlobalSearchProps {
  className?: string;
  placeholder?: string;
  size?: "sm" | "md" | "lg";
  onResultSelect?: (result: SearchResult) => void;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({
  className,
  placeholder = "Search contracts, templates, clauses...",
  size = "md",
  onResultSelect,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [query, setQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);
  const { currentWorkspace } = useClerkWorkspace();
  const navigate = useNavigate();

  // Debounce search query
  const debouncedQuery = useDebounce(query, 300);

  // Size classes
  const sizeClasses = {
    sm: "h-8 text-sm",
    md: "h-9",
    lg: "h-10 text-base",
  };

  const iconSizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5",
  };

  // Load recent searches from localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem("globalSearchRecent");
      if (saved) {
        setRecentSearches(JSON.parse(saved));
      }
    } catch (error) {
      console.warn("Failed to load recent searches:", error);
    }
  }, []);

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((searchTerm: string) => {
    if (!searchTerm.trim()) return;
    
    const updated = [searchTerm, ...recentSearches.filter(s => s !== searchTerm)].slice(0, 5);
    setRecentSearches(updated);
    
    try {
      localStorage.setItem("globalSearchRecent", JSON.stringify(updated));
    } catch (error) {
      console.warn("Failed to save recent search:", error);
    }
  }, [recentSearches]);

  // Perform search
  const performSearch = useCallback(async (searchQuery: string, category: string) => {
    if (!searchQuery.trim() || !currentWorkspace?.id) return;

    setIsLoading(true);
    setError(null);
    
    try {
      const searchResults: SearchResult[] = [];

      // Search contracts
      if (category === "all" || category === "contracts") {
        try {
          const contractsResponse = await ContractService.getContracts({
            workspace_id: currentWorkspace.id,
            search: searchQuery,
            limit: 5,
          });
          
          if (contractsResponse.data) {
            contractsResponse.data.forEach(contract => {
              searchResults.push({
                id: contract.id,
                title: contract.title,
                type: "contract",
                description: contract.description,
                status: contract.status,
                url: `/app/contracts/${contract.id}`,
                metadata: { type: contract.type, created_at: contract.created_at },
              });
            });
          }
        } catch (error) {
          console.warn("Failed to search contracts:", error);
        }
      }

      // Search templates
      if (category === "all" || category === "templates") {
        try {
          const templatesResponse = await TemplateService.getTemplates({
            workspace_id: currentWorkspace.id,
            search: searchQuery,
            limit: 5,
          });
          
          if (templatesResponse.data) {
            templatesResponse.data.forEach(template => {
              searchResults.push({
                id: template.id,
                title: template.title,
                type: "template",
                description: template.description,
                url: `/app/templates/${template.id}`,
                metadata: { type: template.type, industry: template.industry },
              });
            });
          }
        } catch (error) {
          console.warn("Failed to search templates:", error);
        }
      }

      // Search users (if category allows) - TODO: Implement user search API
      if (category === "all" || category === "users") {
        // Placeholder for user search - will be implemented when user search API is available
        console.log("User search not yet implemented");
      }

      setResults(searchResults);
    } catch (error) {
      console.error("Search failed:", error);
      setError("Search failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [currentWorkspace?.id]);

  // Effect to trigger search when debounced query changes
  useEffect(() => {
    if (debouncedQuery) {
      performSearch(debouncedQuery, selectedCategory);
    } else {
      setResults([]);
    }
  }, [debouncedQuery, selectedCategory, performSearch]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + K to focus search
      if ((event.ctrlKey || event.metaKey) && event.key === "k") {
        event.preventDefault();
        inputRef.current?.focus();
        setIsOpen(true);
      }
      
      // Escape to close
      if (event.key === "Escape" && isOpen) {
        setIsOpen(false);
        inputRef.current?.blur();
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => document.removeEventListener("keydown", handleKeyDown);
  }, [isOpen]);

  // Handle result selection
  const handleResultSelect = (result: SearchResult) => {
    saveRecentSearch(query);
    setIsOpen(false);
    setQuery("");
    onResultSelect?.(result);

    // Navigate to result URL using React Router
    if (result.url.startsWith('/app/')) {
      // Use React Router for internal navigation
      navigate(result.url);
    } else {
      // Use window.location for external URLs
      window.location.href = result.url;
    }
  };

  // Handle recent search selection
  const handleRecentSearchSelect = (searchTerm: string) => {
    setQuery(searchTerm);
    inputRef.current?.focus();
  };

  // Clear search
  const handleClear = () => {
    setQuery("");
    setResults([]);
    setError(null);
    inputRef.current?.focus();
  };

  // Get category icon
  const getCategoryIcon = (type: string) => {
    const category = searchCategories.find(c => c.key === type);
    if (!category) return Search;
    return category.icon;
  };

  // Get category color
  const getCategoryColor = (type: string) => {
    const category = searchCategories.find(c => c.key === type);
    return category?.color || "text-muted-foreground";
  };

  return (
    <div className={cn("relative", className)}>
      <div className="relative">
        <Search className={cn(
          "absolute left-2.5 top-1/2 transform -translate-y-1/2 text-muted-foreground transition-colors",
          iconSizeClasses[size],
          query && "text-primary"
        )} />
        
        <Input
          ref={inputRef}
          type="text"
          placeholder={placeholder}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={(e) => {
            if (e.key === "Escape") {
              setIsOpen(false);
              inputRef.current?.blur();
            }
          }}
          className={cn(
            "pl-8 pr-20 bg-background border-[1.5px] border-[hsl(var(--input-border))] focus:border-primary/50 focus:ring-2 focus:ring-primary/20 hover:border-primary/30 transition-all duration-200 shadow-sm",
            sizeClasses[size],
            query && "border-primary/40 shadow-md"
          )}
          aria-label="Global search"
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          role="combobox"
          autoComplete="off"
        />

        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
          {/* Keyboard shortcut hint */}
          {!query && (
            <Badge variant="outline" className="hidden sm:flex h-5 px-1.5 text-xs text-muted-foreground border-border bg-muted/30">
              <CommandIcon className="h-2.5 w-2.5 mr-1" />
              K
            </Badge>
          )}
          
          {/* Clear button */}
          {query && (
            <Button
              variant="ghost"
              size="icon"
              onClick={handleClear}
              className="h-6 w-6"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Search results dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1">
          <Command className="rounded-md border-[1.5px] border-[hsl(var(--dropdown-border))] shadow-lg bg-background">
            <CommandList className="max-h-80">
              {/* Category filters */}
              <CommandGroup heading="Search in">
                <div className="flex flex-wrap gap-1 p-2">
                  {searchCategories.map((category) => {
                    const Icon = category.icon;
                    return (
                      <Button
                        key={category.key}
                        variant={selectedCategory === category.key ? "default" : "ghost"}
                        size="sm"
                        onClick={() => setSelectedCategory(category.key)}
                        className={cn(
                          "h-7 text-xs transition-all duration-200",
                          selectedCategory === category.key
                            ? "bg-primary text-primary-foreground shadow-sm"
                            : "hover:bg-muted/50 hover:text-foreground"
                        )}
                      >
                        <Icon className="h-3 w-3 mr-1" />
                        {category.label}
                      </Button>
                    );
                  })}
                </div>
              </CommandGroup>

              {/* Loading state */}
              {isLoading && (
                <CommandGroup>
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm text-muted-foreground">Searching...</span>
                  </div>
                </CommandGroup>
              )}

              {/* Error state */}
              {error && (
                <CommandGroup>
                  <div className="flex items-center justify-center py-4">
                    <span className="text-sm text-destructive">{error}</span>
                  </div>
                </CommandGroup>
              )}

              {/* Search results */}
              {!isLoading && !error && results.length > 0 && (
                <CommandGroup heading="Results">
                  {results.map((result) => {
                    const Icon = getCategoryIcon(result.type);
                    return (
                      <CommandItem
                        key={`${result.type}-${result.id}`}
                        onSelect={() => handleResultSelect(result)}
                        className="cursor-pointer hover:bg-muted/50 transition-colors duration-200 px-3 py-2"
                      >
                        <Icon className={cn("mr-2 h-4 w-4", getCategoryColor(result.type))} />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{result.title}</div>
                          {result.description && (
                            <div className="text-xs text-muted-foreground truncate">
                              {result.description}
                            </div>
                          )}
                        </div>
                        {result.status && (
                          <Badge variant="outline" className="ml-2 text-xs">
                            {result.status}
                          </Badge>
                        )}
                      </CommandItem>
                    );
                  })}
                </CommandGroup>
              )}

              {/* Recent searches */}
              {!query && recentSearches.length > 0 && (
                <CommandGroup heading="Recent Searches">
                  {recentSearches.map((search, index) => (
                    <CommandItem
                      key={index}
                      onSelect={() => handleRecentSearchSelect(search)}
                      className="cursor-pointer hover:bg-muted/50 transition-colors duration-200 px-3 py-2"
                    >
                      <Clock className="mr-2 h-3 w-3 text-muted-foreground" />
                      {search}
                    </CommandItem>
                  ))}
                </CommandGroup>
              )}

              {/* Empty state */}
              {!isLoading && !error && query && results.length === 0 && (
                <CommandEmpty>No results found for "{query}"</CommandEmpty>
              )}
              
              {/* No query state */}
              {!query && recentSearches.length === 0 && (
                <CommandEmpty>
                  <div className="text-center py-4">
                    <Search className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                    <p className="text-sm text-muted-foreground">
                      Start typing to search across contracts, templates, and more
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Press <kbd className="px-1 py-0.5 bg-muted rounded text-xs">Ctrl+K</kbd> to focus
                    </p>
                  </div>
                </CommandEmpty>
              )}
            </CommandList>
          </Command>
        </div>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default GlobalSearch;
