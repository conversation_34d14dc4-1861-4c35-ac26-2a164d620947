import React from "react";
import { Button } from "@/components/ui/button";
import ModernDocumentRepository from "./ModernDocumentRepository";
import { ArrowLeft, Upload, FolderPlus, MoreHorizontal, Plus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";

interface RepositoryPageProps {
  onSelectTemplate?: (templateId: string) => void;
}

const RepositoryPage = ({ onSelectTemplate }: RepositoryPageProps) => {
  return (
    <div className="page-container">
      <div className="page-header">
        <div className="space-y-1">
          <p className="body-text-secondary">
            Manage your templates, contracts, and documents
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="default"
            size="sm"
            className="flex items-center gap-1.5"
            onClick={() => {
              // TODO: Implement new template functionality
              console.log("Create new template");
            }}
          >
            <Plus className="h-4 w-4" />
            New Template
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="flex items-center gap-1.5">
                <MoreHorizontal className="h-4 w-4" />
                More Actions
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => console.log("Upload document")}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => console.log("Create new folder")}>
                <FolderPlus className="h-4 w-4 mr-2" />
                New Folder
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => (window.location.href = "/app")}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <ModernDocumentRepository onSelectTemplate={onSelectTemplate} />
    </div>
  );
};

export default RepositoryPage;
